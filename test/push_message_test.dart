import 'package:flutter_test/flutter_test.dart';
import 'package:jiguang_push_plugin/push_message.dart';

void main() {
  group('PushMessage', () {
    test('should parse simple push message correctly', () {
      final data = {
        'path': '/goodsDetail?id=123',
        'extras': {
          'key1': 'value1',
          'key2': 'value2',
        },
      };

      final message = PushMessage.fromMap(data);

      expect(message.path, equals('/goodsDetail?id=123'));
      expect(message.extras, isNotNull);
      expect(message.extras!['key1'], equals('value1'));
      expect(message.extras!['key2'], equals('value2'));
    });

    test('should parse push message without extras', () {
      final data = {
        'path': '/home',
      };

      final message = PushMessage.fromMap(data);

      expect(message.path, equals('/home'));
      expect(message.extras, isNull);
    });

    test('should parse complex push message with nested data', () {
      // 模拟你遇到的实际数据结构
      final data = {
        'path': 'goodsDetail?id=009',
        'extras': {
          'key2': 2,
          'JMessageExtra': {
            'ad_t': 0,
            'n_alert_type': 7,
            'n_badge_add_num': 1,
            'n_category': '',
            'n_content': '加入我们的直播，抢先体验即将上线的新游戏，主播将带你深入解析玩法和亮点！',
            'n_extras': {},
            'n_flag': 1,
            'n_priority': 0,
            'n_sound': '',
            'n_style': 0,
            'n_title': '新游戏首曝：直播抢先体验',
            'msg_id': '18102430290226618',
            'rom_type': '0',
            'notification_id': 510657384,
            '_j_data_': '{"data_msgtype":1,"push_type":8, "is_vip":0}',
          },
        },
      };

      final message = PushMessage.fromMap(data);

      expect(message.path, equals('goodsDetail?id=009'));
      expect(message.extras, isNotNull);
      expect(message.extras!['key2'], equals(2));
      expect(message.extras!['JMessageExtra'], isA<Map<String, dynamic>>());
      
      final jMessageExtra = message.extras!['JMessageExtra'] as Map<String, dynamic>;
      expect(jMessageExtra['n_title'], equals('新游戏首曝：直播抢先体验'));
      expect(jMessageExtra['notification_id'], equals(510657384));
    });

    test('should handle Map<Object?, Object?> type conversion', () {
      // 模拟从原生端接收到的 Map<Object?, Object?> 类型数据
      final Map<Object?, Object?> rawData = {
        'path': 'goodsDetail?id=009',
        'extras': {
          'key2': 2,
          'nested': {
            'value': 'test',
          },
        },
      };

      // 转换为 Map<String, dynamic>
      final convertedData = Map<String, dynamic>.from(rawData);
      final message = PushMessage.fromMap(convertedData);

      expect(message.path, equals('goodsDetail?id=009'));
      expect(message.extras, isNotNull);
      expect(message.extras!['key2'], equals(2));
      expect(message.extras!['nested'], isA<Map<String, dynamic>>());
    });

    test('should convert toString correctly', () {
      final message = PushMessage(
        path: '/test',
        extras: {'key': 'value'},
      );

      final string = message.toString();
      expect(string, contains('PushMessage'));
      expect(string, contains('/test'));
      expect(string, contains('key'));
    });

    test('should convert toMap correctly', () {
      final message = PushMessage(
        path: '/test',
        extras: {'key': 'value'},
      );

      final map = message.toMap();
      expect(map['path'], equals('/test'));
      expect(map['extras'], equals({'key': 'value'}));
    });

    test('should handle equality correctly', () {
      final message1 = PushMessage(
        path: '/test',
        extras: {'key': 'value'},
      );

      final message2 = PushMessage(
        path: '/test',
        extras: {'key': 'value'},
      );

      final message3 = PushMessage(
        path: '/different',
        extras: {'key': 'value'},
      );

      expect(message1, equals(message2));
      expect(message1, isNot(equals(message3)));
    });
  });
}

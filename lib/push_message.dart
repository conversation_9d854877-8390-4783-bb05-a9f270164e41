/// 推送消息数据模型
class PushMessage {
  /// 跳转路径，如 "/goodsDetail?id=111"
  final String path;
  
  /// 额外数据（可选）
  final Map<String, dynamic>? extras;

  const PushMessage({
    required this.path,
    this.extras,
  });

  /// 从 Map 创建 PushMessage 实例
  factory PushMessage.fromMap(Map<String, dynamic> map) {
    // 安全地转换 extras
    Map<String, dynamic>? extras;
    if (map['extras'] != null) {
      if (map['extras'] is Map) {
        extras = _convertMapRecursively(map['extras'] as Map);
      }
    }

    return PushMessage(
      path: map['path'] as String,
      extras: extras,
    );
  }

  /// 递归转换 Map，确保所有嵌套的 Map 都是 Map&lt;String, dynamic&gt; 类型
  static Map<String, dynamic> _convertMapRecursively(Map map) {
    final result = <String, dynamic>{};
    for (final entry in map.entries) {
      final key = entry.key.toString();
      final value = entry.value;

      if (value is Map) {
        result[key] = _convertMapRecursively(value);
      } else if (value is List) {
        result[key] = _convertListRecursively(value);
      } else {
        result[key] = value;
      }
    }
    return result;
  }

  /// 递归转换 List，确保嵌套的 Map 都是正确类型
  static List<dynamic> _convertListRecursively(List list) {
    return list.map((item) {
      if (item is Map) {
        return _convertMapRecursively(item);
      } else if (item is List) {
        return _convertListRecursively(item);
      } else {
        return item;
      }
    }).toList();
  }

  /// 转换为 Map
  Map<String, dynamic> toMap() {
    return {
      'path': path,
      if (extras != null) 'extras': extras,
    };
  }

  @override
  String toString() {
    return 'PushMessage{path: $path, extras: $extras}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PushMessage &&
        other.path == path &&
        _mapEquals(other.extras, extras);
  }

  @override
  int get hashCode => path.hashCode ^ extras.hashCode;

  /// 比较两个 Map 是否相等
  bool _mapEquals(Map<String, dynamic>? a, Map<String, dynamic>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}

/// 推送消息回调函数类型
typedef PushMessageCallback = void Function(PushMessage message);

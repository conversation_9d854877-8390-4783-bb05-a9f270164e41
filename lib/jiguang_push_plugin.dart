
import 'jiguang_push_plugin_platform_interface.dart';

class JiguangPushPlugin {
  Future<String?> getPlatformVersion() {
    return JiguangPushPluginPlatform.instance.getPlatformVersion();
  }

  /// 初始化极光推送
  ///
  /// 返回包含初始化结果的Map，包含success和message字段
  ///
  /// 示例:
  /// ```dart
  /// final result = await JiguangPushPlugin.initPush();
  /// if (result?['success'] == true) {
  ///   print('推送初始化成功: ${result?['message']}');
  /// }
  /// ```
  static Future<Map<String, dynamic>?> initPush() {
    return JiguangPushPluginPlatform.instance.initPush();
  }

  /// 停止推送服务
  ///
  /// 返回包含操作结果的Map，包含success和message字段
  ///
  /// 示例:
  /// ```dart
  /// final result = await JiguangPushPlugin.stopPush();
  /// if (result?['success'] == true) {
  ///   print('推送服务已停止');
  /// }
  /// ```
  static Future<Map<String, dynamic>?> stopPush() {
    return JiguangPushPluginPlatform.instance.stopPush();
  }

  /// 恢复推送服务
  ///
  /// 返回包含操作结果的Map，包含success和message字段
  ///
  /// 示例:
  /// ```dart
  /// final result = await JiguangPushPlugin.resumePush();
  /// if (result?['success'] == true) {
  ///   print('推送服务已恢复');
  /// }
  /// ```
  static Future<Map<String, dynamic>?> resumePush() {
    return JiguangPushPluginPlatform.instance.resumePush();
  }

  /// 获取注册ID
  ///
  /// 返回设备的注册ID字符串，用于服务端推送时指定设备
  ///
  /// 示例:
  /// ```dart
  /// final registrationId = await JiguangPushPlugin.getRegistrationId();
  /// print('设备注册ID: $registrationId');
  /// ```
  static Future<String?> getRegistrationId() {
    return JiguangPushPluginPlatform.instance.getRegistrationId();
  }

  /// 检查推送服务是否停止
  ///
  /// 返回true表示推送服务已停止，false表示正在运行
  ///
  /// 示例:
  /// ```dart
  /// final isStopped = await JiguangPushPlugin.isPushStopped();
  /// if (isStopped == true) {
  ///   print('推送服务已停止');
  /// } else {
  ///   print('推送服务正在运行');
  /// }
  /// ```
  static Future<bool?> isPushStopped() {
    return JiguangPushPluginPlatform.instance.isPushStopped();
  }

  /// 用于引导用户开启通知权限
  ///
  /// 示例:
  /// ```dart
  /// await JiguangPushPlugin.goToAppNotificationSettings();
  /// ```
  static Future<void> goToAppNotificationSettings() {
    return JiguangPushPluginPlatform.instance.goToAppNotificationSettings();
  }

  /// 检查通知是否开启
  ///
  /// 返回true表示通知已开启，false表示通知未开启
  ///
  /// 示例:
  /// ```dart
  /// final isEnabled = await JiguangPushPlugin.isNotificationEnabled();
  /// if (isEnabled == true) {
  ///   print('通知已开启');
  /// } else {
  ///   print('通知未开启');
  /// }
  /// ```
  static Future<bool> isNotificationEnabled() {
    return JiguangPushPluginPlatform.instance.isNotificationEnabled();
  }
}

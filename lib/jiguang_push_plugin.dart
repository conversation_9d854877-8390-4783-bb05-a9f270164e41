
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'jiguang_push_plugin_platform_interface.dart';
import 'push_message.dart';

export 'push_message.dart';

class JiguangPushPlugin {
  /// 推送消息回调
  static PushMessageCallback? _onPushMessage;

  /// 推送消息事件通道
  static const EventChannel _eventChannel =
      EventChannel('jiguang_push_plugin/push_message');

  /// 是否已经开始监听推送消息
  static bool _isListening = false;
  Future<String?> getPlatformVersion() {
    return JiguangPushPluginPlatform.instance.getPlatformVersion();
  }

  /// 初始化极光推送
  ///
  /// 返回包含初始化结果的Map，包含success和message字段
  ///
  /// 示例:
  /// ```dart
  /// final result = await JiguangPushPlugin.initPush();
  /// if (result?['success'] == true) {
  ///   print('推送初始化成功: ${result?['message']}');
  /// }
  /// ```
  static Future<Map<String, dynamic>?> initPush() {
    return JiguangPushPluginPlatform.instance.initPush();
  }

  /// 停止推送服务
  ///
  /// 返回包含操作结果的Map，包含success和message字段
  ///
  /// 示例:
  /// ```dart
  /// final result = await JiguangPushPlugin.stopPush();
  /// if (result?['success'] == true) {
  ///   print('推送服务已停止');
  /// }
  /// ```
  static Future<Map<String, dynamic>?> stopPush() {
    return JiguangPushPluginPlatform.instance.stopPush();
  }

  /// 恢复推送服务
  ///
  /// 返回包含操作结果的Map，包含success和message字段
  ///
  /// 示例:
  /// ```dart
  /// final result = await JiguangPushPlugin.resumePush();
  /// if (result?['success'] == true) {
  ///   print('推送服务已恢复');
  /// }
  /// ```
  static Future<Map<String, dynamic>?> resumePush() {
    return JiguangPushPluginPlatform.instance.resumePush();
  }

  /// 获取注册ID
  ///
  /// 返回设备的注册ID字符串，用于服务端推送时指定设备
  ///
  /// 示例:
  /// ```dart
  /// final registrationId = await JiguangPushPlugin.getRegistrationId();
  /// print('设备注册ID: $registrationId');
  /// ```
  static Future<String?> getRegistrationId() {
    return JiguangPushPluginPlatform.instance.getRegistrationId();
  }

  /// 检查推送服务是否停止
  ///
  /// 返回true表示推送服务已停止，false表示正在运行
  ///
  /// 示例:
  /// ```dart
  /// final isStopped = await JiguangPushPlugin.isPushStopped();
  /// if (isStopped == true) {
  ///   print('推送服务已停止');
  /// } else {
  ///   print('推送服务正在运行');
  /// }
  /// ```
  static Future<bool?> isPushStopped() {
    return JiguangPushPluginPlatform.instance.isPushStopped();
  }

  /// 用于引导用户开启通知权限
  ///
  /// 示例:
  /// ```dart
  /// await JiguangPushPlugin.goToAppNotificationSettings();
  /// ```
  static Future<void> goToAppNotificationSettings() {
    return JiguangPushPluginPlatform.instance.goToAppNotificationSettings();
  }

  /// 检查通知是否开启
  ///
  /// 返回true表示通知已开启，false表示通知未开启
  ///
  /// 示例:
  /// ```dart
  /// final isEnabled = await JiguangPushPlugin.isNotificationEnabled();
  /// if (isEnabled == true) {
  ///   print('通知已开启');
  /// } else {
  ///   print('通知未开启');
  /// }
  /// ```
  static Future<bool> isNotificationEnabled() {
    return JiguangPushPluginPlatform.instance.isNotificationEnabled();
  }

  /// 设置推送消息回调
  ///
  /// 设置回调的同时会自动开始监听推送消息
  ///
  /// 示例:
  /// ```dart
  /// JiguangPushPlugin.setPushMessageCallback((message) {
  ///   print('收到推送消息: ${message.path}');
  ///   // 根据 path 进行页面跳转
  ///   navigateToPage(message.path, message.extras);
  /// });
  /// ```
  static void setPushMessageCallback(PushMessageCallback callback) {
    _onPushMessage = callback;
    _startListeningEventChannel();
  }

  /// 检查暂存的推送消息
  ///
  /// 应用完全启动后调用此方法，检查是否有冷启动时暂存的推送消息
  ///
  /// 示例:
  /// ```dart
  /// // 在主页完全启动后调用
  /// await JiguangPushPlugin.checkPendingMessage();
  /// ```
  static Future<void> checkPendingMessage() {
    return JiguangPushPluginPlatform.instance.checkPendingMessage();
  }

  /// 发送测试推送消息（用于调试）
  ///
  /// 用于测试推送消息功能是否正常工作
  ///
  /// 示例:
  /// ```dart
  /// // 发送默认测试消息
  /// await JiguangPushPlugin.testPushMessage();
  ///
  /// // 发送自定义路径的测试消息
  /// await JiguangPushPlugin.testPushMessage(path: '/custom?id=456');
  /// ```
  static Future<String?> testPushMessage({String? path}) {
    return JiguangPushPluginPlatform.instance.testPushMessage(path: path);
  }

  /// 开始监听推送消息事件通道
  static void _startListeningEventChannel() {
    if (_isListening) {
      return;
    }

    if (kDebugMode) {
      print('Starting to listen to push message EventChannel');
    }

    _isListening = true;
    _eventChannel.receiveBroadcastStream().listen(
      (dynamic data) {
        try {
          if (kDebugMode) {
            print('Received push message data: $data');
          }

          // 安全地转换数据类型
          Map<String, dynamic>? messageData;
          if (data is Map) {
            // 将 Map<Object?, Object?> 转换为 Map<String, dynamic>
            messageData = Map<String, dynamic>.from(data);
          } else {
            if (kDebugMode) {
              print('Received data is not Map: ${data.runtimeType}');
            }
            return;
          }

          final message = PushMessage.fromMap(messageData);
          if (kDebugMode) {
            print('Parsed push message: $message');
          }
          _onPushMessage?.call(message);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing push message: $e');
            print('Raw data: $data');
          }
        }
      },
      onError: (dynamic error) {
        if (kDebugMode) {
          print('Push message event channel error: $error');
        }
      },
      onDone: () {
        if (kDebugMode) {
          print('Push message event channel done');
        }
        _isListening = false;
      },
    );
  }
}

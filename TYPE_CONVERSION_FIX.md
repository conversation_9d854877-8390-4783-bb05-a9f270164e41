# 数据类型转换问题修复

## 问题描述

在推送消息从 Android 端发送到 Flutter 端时，遇到了数据类型转换问题：

```
I/flutter: Received push message data: {path: goodsDetail?id=009, extras: {...}}
I/flutter: Received data is not Map<String, dynamic>: _Map<Object?, Object?>
```

## 问题原因

1. **类型不匹配**：Android 端发送的数据类型是 `_Map<Object?, Object?>`，但 Flutter 端期望的是 `Map<String, dynamic>`
2. **嵌套数据结构**：推送数据中包含复杂的嵌套 Map 结构，需要递归转换
3. **类型检查过于严格**：原始代码只接受 `Map<String, dynamic>` 类型，拒绝了其他 Map 类型

## 解决方案

### 1. 改进数据接收逻辑

在 `lib/jiguang_push_plugin.dart` 中：

```dart
// 原始代码
if (data is Map<String, dynamic>) {
  final message = PushMessage.fromMap(data);
  // ...
}

// 修复后的代码
Map<String, dynamic>? messageData;
if (data is Map) {
  // 将 Map<Object?, Object?> 转换为 Map<String, dynamic>
  messageData = Map<String, dynamic>.from(data);
} else {
  return; // 不是 Map 类型，直接返回
}

final message = PushMessage.fromMap(messageData);
```

### 2. 改进 PushMessage.fromMap 方法

在 `lib/push_message.dart` 中添加了递归转换逻辑：

```dart
factory PushMessage.fromMap(Map<String, dynamic> map) {
  // 安全地转换 extras
  Map<String, dynamic>? extras;
  if (map['extras'] != null) {
    if (map['extras'] is Map) {
      extras = _convertMapRecursively(map['extras'] as Map);
    }
  }
  
  return PushMessage(
    path: map['path'] as String,
    extras: extras,
  );
}

/// 递归转换 Map，确保所有嵌套的 Map 都是 Map<String, dynamic> 类型
static Map<String, dynamic> _convertMapRecursively(Map map) {
  final result = <String, dynamic>{};
  for (final entry in map.entries) {
    final key = entry.key.toString();
    final value = entry.value;
    
    if (value is Map) {
      result[key] = _convertMapRecursively(value);
    } else if (value is List) {
      result[key] = _convertListRecursively(value);
    } else {
      result[key] = value;
    }
  }
  return result;
}
```

### 3. 处理复杂的推送数据结构

你的推送数据包含了极光推送的复杂结构：

```json
{
  "path": "goodsDetail?id=009",
  "extras": {
    "key2": 2,
    "JMessageExtra": {
      "n_title": "新游戏首曝：直播抢先体验",
      "n_content": "加入我们的直播，抢先体验即将上线的新游戏...",
      "notification_id": 510657384,
      "_j_data_": "{\"data_msgtype\":1,\"push_type\":8, \"is_vip\":0}"
    }
  }
}
```

现在可以正确解析并访问这些数据：

```dart
JiguangPushPlugin.setPushMessageCallback((message) {
  print('路径: ${message.path}'); // goodsDetail?id=009
  
  if (message.extras != null) {
    final jMessageExtra = message.extras!['JMessageExtra'] as Map<String, dynamic>?;
    if (jMessageExtra != null) {
      final title = jMessageExtra['n_title'] as String?;
      final content = jMessageExtra['n_content'] as String?;
      print('标题: $title');
      print('内容: $content');
    }
  }
});
```

## 测试验证

### 1. 单元测试
创建了 `test/push_message_test.dart` 来验证各种数据格式的解析：

- 简单推送消息
- 无 extras 的消息
- 复杂嵌套结构的消息
- Map<Object?, Object?> 类型转换

### 2. 示例应用
创建了 `example/lib/push_message_example.dart` 来演示：

- 推送消息的接收和处理
- 复杂数据结构的解析
- 极光推送特定字段的处理

## 关键改进点

1. **类型容错**：接受任何 Map 类型，自动转换为所需格式
2. **递归转换**：处理任意深度的嵌套数据结构
3. **安全解析**：添加了空值检查和类型验证
4. **调试友好**：增加了详细的日志输出

## 使用建议

1. **设置回调后立即检查暂存消息**：
```dart
JiguangPushPlugin.setPushMessageCallback((message) {
  // 处理推送消息
});
await JiguangPushPlugin.checkPendingMessage();
```

2. **处理极光推送的特殊字段**：
```dart
if (message.extras?.containsKey('JMessageExtra') == true) {
  final jExtra = message.extras!['JMessageExtra'] as Map<String, dynamic>;
  // 处理极光推送的特定数据
}
```

3. **使用测试功能验证**：
```dart
await JiguangPushPlugin.testPushMessage(path: 'goodsDetail?id=009');
```

现在推送消息应该能够正确解析和处理了！

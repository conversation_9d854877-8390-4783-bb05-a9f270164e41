# 推送消息处理使用指南

本文档介绍如何使用 `jiguang_push_plugin` 处理推送消息，包括热启动和冷启动两种场景。

## 功能概述

插件支持两种推送消息处理场景：

1. **热启动**：APP 在前台或后台时收到推送，用户点击通知
2. **冷启动**：APP 被杀死时收到推送，用户点击通知启动 APP

## 数据结构

### PushMessage

```dart
class PushMessage {
  /// 跳转路径，如 "/goodsDetail?id=111"
  final String path;
  
  /// 额外数据（可选）
  final Map<String, dynamic>? extras;
}
```

### PushMessageCallback

```dart
typedef PushMessageCallback = void Function(PushMessage message);
```

## 基本使用

### 1. 设置推送消息回调

在应用启动后（通常在主页完全加载后）设置推送消息回调：

```dart
void setupPushMessage() {
  // 设置推送消息回调
  JiguangPushPlugin.setPushMessageCallback((message) {
    print('收到推送消息: ${message.path}');
    
    // 根据 path 进行页面跳转
    navigateToPage(message.path, message.extras);
  });
}
```

### 2. 检查暂存的推送消息

在设置回调后，检查是否有冷启动时暂存的推送消息：

```dart
Future<void> checkPendingMessage() async {
  try {
    await JiguangPushPlugin.checkPendingMessage();
  } catch (e) {
    print('检查暂存消息失败: $e');
  }
}
```

### 3. 完整示例

```dart
class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // 等待页面完全加载后再设置推送消息处理
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupPushMessage();
    });
  }

  void _setupPushMessage() {
    // 设置推送消息回调
    JiguangPushPlugin.setPushMessageCallback((message) {
      _handlePushMessage(message);
    });
    
    // 检查暂存的推送消息
    JiguangPushPlugin.checkPendingMessage();
  }

  void _handlePushMessage(PushMessage message) {
    // 处理推送消息
    switch (message.path) {
      case '/goodsDetail':
        // 跳转到商品详情页
        final goodsId = message.extras?['id'];
        Navigator.pushNamed(context, '/goodsDetail', arguments: goodsId);
        break;
      case '/orderDetail':
        // 跳转到订单详情页
        final orderId = message.extras?['orderId'];
        Navigator.pushNamed(context, '/orderDetail', arguments: orderId);
        break;
      default:
        // 默认处理
        print('未知的推送路径: ${message.path}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      // 你的应用内容
    );
  }
}
```

## 工作原理

### 热启动流程

1. 用户点击推送通知
2. 触发 `onNewIntent` 方法
3. 解析推送数据，通过 `EventChannel` 直接发送给 Flutter
4. Flutter 端接收到消息，调用设置的回调函数

### 冷启动流程

1. 用户点击推送通知
2. 触发 `onAttachedToActivity` 方法
3. 解析推送数据，暂存到静态变量中
4. Flutter 端启动后调用 `checkPendingMessage`
5. 原生端检查暂存数据，通过 `EventChannel` 发送给 Flutter
6. 发送后清除暂存数据
7. Flutter 端接收到消息，调用设置的回调函数

## 注意事项

1. **时机控制**：确保在应用完全启动后再设置推送消息回调
2. **去重机制**：暂存的推送消息在发送给 Flutter 后会自动清除
3. **单条暂存**：只会保存最新一条冷启动推送消息
4. **错误处理**：建议在回调中添加适当的错误处理逻辑

## 推送数据格式

原生端需要在 Intent 中设置以下数据：

- `path`：跳转路径（必需）
- 其他自定义字段可以作为 extras 传递

示例：
```java
Intent intent = new Intent();
intent.setAction("com.changqing.health");
intent.putExtra("path", "/goodsDetail?id=123");
// 其他额外数据...
```

## 调试建议

1. 在回调函数中添加日志输出
2. 使用示例应用测试推送消息处理
3. 分别测试热启动和冷启动场景
4. 检查 Android 端的日志输出

package com.cq.jiguang_push_plugin_example;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

import io.flutter.embedding.android.FlutterActivity;

public class MainActivity extends FlutterActivity {
    private static final String TAG = "MainActivity";
    private static final String PUSH_ACTION = "com.changqing.health";
    private static final String JPUSH_ACTION = "cn.jpush.android.intent.JNotifyActivity";

    private static final List<String> pushActions = List.of(PUSH_ACTION, JPUSH_ACTION);

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        if (pushActions.contains(intent.getAction())) {
            onNewIntent(intent);
        }
    }

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);
        if (pushActions.contains(intent.getAction())) {
            String value1 = intent.getStringExtra("path");
            int value2 = intent.getIntExtra("extras", 0);
            Log.e(TAG, "onNewIntent: value1: " + value1 + ", value2: " + value2);
            // 显示 Dialog 展示接收到的值
            showReceivedDataDialog(value1, value2, intent);
        }
    }

    /**
     * 显示接收到的数据的 Dialog
     */
    private void showReceivedDataDialog(String value1, int value2, Intent intent) {
        StringBuilder message = new StringBuilder();
        message.append("接收到的数据:\n\n");

        // 显示主要的值
        message.append("path: ").append(value1 != null ? value1 : "null").append("\n");
        message.append("extras: ").append(value2).append("\n\n");

        // 显示所有的 extras
        if (intent.getExtras() != null) {
            message.append("所有 Intent Extras:\n");
            for (String key : intent.getExtras().keySet()) {
                Object value = intent.getExtras().get(key);
                message.append(key).append(": ").append(value != null ? value.toString() : "null").append("\n");
            }
        }

        // 显示 Intent 的其他信息
        message.append("\nIntent 信息:\n");
        message.append("Action: ").append(intent.getAction() != null ? intent.getAction() : "null").append("\n");
        message.append("Data: ").append(intent.getData() != null ? intent.getData().toString() : "null").append("\n");

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("onNewIntent 接收到的数据")
                .setMessage(message.toString())
                .setPositiveButton("确定", (dialog, which) -> dialog.dismiss())
                .setNegativeButton("复制到剪贴板", (dialog, which) -> {
                    // 复制到剪贴板
                    android.content.ClipboardManager clipboard =
                            (android.content.ClipboardManager) getSystemService(android.content.Context.CLIPBOARD_SERVICE);
                    android.content.ClipData clip = android.content.ClipData.newPlainText("Intent Data", message.toString());
                    clipboard.setPrimaryClip(clip);

                    // 显示提示
                    android.widget.Toast.makeText(this, "已复制到剪贴板", android.widget.Toast.LENGTH_SHORT).show();
                    dialog.dismiss();
                })
                .setCancelable(true)
                .show();
    }
}

import 'package:flutter/material.dart';
import 'package:jiguang_push_plugin/jiguang_push_plugin.dart';

/// 推送消息处理示例
class PushMessageExample extends StatefulWidget {
  const PushMessageExample({super.key});

  @override
  State<PushMessageExample> createState() => _PushMessageExampleState();
}

class _PushMessageExampleState extends State<PushMessageExample> {
  final List<PushMessage> _receivedMessages = [];

  @override
  void initState() {
    super.initState();
    _setupPushMessageHandling();
  }

  void _setupPushMessageHandling() {
    // 设置推送消息回调
    JiguangPushPlugin.setPushMessageCallback((message) {
      setState(() {
        _receivedMessages.insert(0, message);
      });
      
      // 处理推送消息
      _handlePushMessage(message);
    });
    
    // 检查暂存的推送消息
    JiguangPushPlugin.checkPendingMessage();
  }

  void _handlePushMessage(PushMessage message) {
    // 根据路径进行不同的处理
    switch (message.path) {
      case 'goodsDetail':
        _handleGoodsDetail(message);
        break;
      default:
        _showPushMessageDialog(message);
    }
  }

  void _handleGoodsDetail(PushMessage message) {
    // 处理商品详情推送
    final extras = message.extras;
    if (extras != null) {
      // 检查是否有极光推送的额外数据
      if (extras.containsKey('JMessageExtra')) {
        final jMessageExtra = extras['JMessageExtra'] as Map<String, dynamic>?;
        if (jMessageExtra != null) {
          final title = jMessageExtra['n_title'] as String?;
          final content = jMessageExtra['n_content'] as String?;
          
          _showJiguangMessageDialog(title, content, message);
          return;
        }
      }
    }
    
    // 默认处理
    _showPushMessageDialog(message);
  }

  void _showJiguangMessageDialog(String? title, String? content, PushMessage message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? '推送消息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (content != null) ...[
              Text(content),
              const SizedBox(height: 16),
            ],
            Text('路径: ${message.path}'),
            if (message.extras != null) ...[
              const SizedBox(height: 8),
              Text('额外数据: ${message.extras!.length} 项'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showRawDataDialog(message);
            },
            child: const Text('查看原始数据'),
          ),
        ],
      ),
    );
  }

  void _showPushMessageDialog(PushMessage message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('收到推送消息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('路径: ${message.path}'),
            if (message.extras != null) ...[
              const SizedBox(height: 8),
              Text('额外数据: ${message.extras!.length} 项'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showRawDataDialog(message);
            },
            child: const Text('查看详细数据'),
          ),
        ],
      ),
    );
  }

  void _showRawDataDialog(PushMessage message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('推送消息详细数据'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('路径: ${message.path}'),
              const SizedBox(height: 16),
              if (message.extras != null) ...[
                const Text('额外数据:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...message.extras!.entries.map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text('${entry.key}: ${entry.value}'),
                )),
              ] else
                const Text('无额外数据'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('推送消息示例'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      await JiguangPushPlugin.testPushMessage(
                        path: 'goodsDetail?id=009',
                      );
                    },
                    child: const Text('发送测试消息'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      await JiguangPushPlugin.checkPendingMessage();
                    },
                    child: const Text('检查暂存消息'),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _receivedMessages.isEmpty
                ? const Center(
                    child: Text('暂无推送消息'),
                  )
                : ListView.builder(
                    itemCount: _receivedMessages.length,
                    itemBuilder: (context, index) {
                      final message = _receivedMessages[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        child: ListTile(
                          title: Text(message.path),
                          subtitle: message.extras != null
                              ? Text('${message.extras!.length} 项额外数据')
                              : const Text('无额外数据'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () => _showRawDataDialog(message),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}

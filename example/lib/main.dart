import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:jiguang_push_plugin/jiguang_push_plugin.dart';
import 'push_example.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _platformVersion = 'Unknown';
  String _registrationId = '未获取';
  bool _isPushStopped = false;
  String _statusMessage = '准备就绪';
  bool _isNotificationEnabled = false;
  final _jiguangPushPlugin = JiguangPushPlugin();

  @override
  void initState() {
    super.initState();
    initPlatformState();
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    String platformVersion;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      platformVersion = await _jiguangPushPlugin.getPlatformVersion() ??
          'Unknown platform version';
    } on PlatformException {
      platformVersion = 'Failed to get platform version.';
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    setState(() {
      _platformVersion = platformVersion;
    });
  }

  /// 初始化推送服务
  Future<void> _initializePush() async {
    try {
      final result = await JiguangPushPlugin.initPush();
      if (result?['success'] == true) {
        setState(() {
          _statusMessage = '推送初始化成功: ${result?['message']}';
        });
        _getRegistrationId();
        _checkPushStatus();
        _checkNotificationStatus();
      } else {
        setState(() {
          _statusMessage = '推送初始化失败';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '初始化异常: $e';
      });
    }
  }

  /// 获取注册ID
  Future<void> _getRegistrationId() async {
    try {
      final registrationId = await JiguangPushPlugin.getRegistrationId();
      setState(() {
        _registrationId = registrationId ?? '获取失败';
      });
    } catch (e) {
      setState(() {
        _registrationId = '获取异常: $e';
      });
    }
  }

  /// 停止推送服务
  Future<void> _stopPush() async {
    try {
      final result = await JiguangPushPlugin.stopPush();
      if (result?['success'] == true) {
        setState(() {
          _statusMessage = '推送服务已停止: ${result?['message']}';
        });
        _checkPushStatus();
      } else {
        setState(() {
          _statusMessage = '停止推送失败';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '停止推送异常: $e';
      });
    }
  }

  /// 恢复推送服务
  Future<void> _resumePush() async {
    try {
      final result = await JiguangPushPlugin.resumePush();
      if (result?['success'] == true) {
        setState(() {
          _statusMessage = '推送服务已恢复: ${result?['message']}';
        });
        _checkPushStatus();
      } else {
        setState(() {
          _statusMessage = '恢复推送失败';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '恢复推送异常: $e';
      });
    }
  }

  /// 检查推送状态
  Future<void> _checkPushStatus() async {
    try {
      final isStopped = await JiguangPushPlugin.isPushStopped();
      setState(() {
        _isPushStopped = isStopped ?? true;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '检查状态异常: $e';
      });
    }
  }

  Future<void> _checkNotificationStatus() async {
    try {
      final isEnabled = await JiguangPushPlugin.isNotificationEnabled();
      setState(() {
        _isNotificationEnabled = isEnabled;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '检查通知状态异常: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '极光推送插件示例',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('极光推送插件示例'),
          actions: [
            IconButton(
              icon: const Icon(Icons.info),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const PushExample()),
                );
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 平台信息
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '平台信息',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text('运行平台: $_platformVersion'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 推送状态信息
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('推送状态',
                          style: Theme.of(context).textTheme.titleLarge),
                      const SizedBox(height: 8),
                      Text("通知开关: ${_isNotificationEnabled ? "开启" : "关闭"}"),
                      const SizedBox(height: 8),
                      Text('状态: $_statusMessage'),
                      const SizedBox(height: 8),
                      Text('推送服务: ${_isPushStopped ? "已停止" : "运行中"}'),
                      const SizedBox(height: 8),
                      SelectableText('注册ID: $_registrationId'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 操作按钮
              ElevatedButton.icon(
                onPressed: _initializePush,
                icon: const Icon(Icons.play_arrow),
                label: const Text('初始化推送'),
              ),
              const SizedBox(height: 8),

              ElevatedButton.icon(
                onPressed: _getRegistrationId,
                icon: const Icon(Icons.fingerprint),
                label: const Text('获取注册ID'),
              ),
              const SizedBox(height: 8),

              ElevatedButton.icon(
                onPressed: _isPushStopped ? null : _stopPush,
                icon: const Icon(Icons.stop),
                label: const Text('停止推送'),
              ),
              const SizedBox(height: 8),

              ElevatedButton.icon(
                onPressed: _isPushStopped ? _resumePush : null,
                icon: const Icon(Icons.play_arrow),
                label: const Text('恢复推送'),
              ),
              const SizedBox(height: 8),

              ElevatedButton.icon(
                onPressed: _checkPushStatus,
                icon: const Icon(Icons.refresh),
                label: const Text('检查推送状态'),
              ),
              const SizedBox(height: 16),

              // 详细示例按钮
              OutlinedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PushExample()),
                  );
                },
                icon: const Icon(Icons.code),
                label: const Text('查看详细示例'),
              ),

              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () async {
                  await JiguangPushPlugin.goToAppNotificationSettings();
                },
                icon: const Icon(Icons.settings),
                label: const Text('跳转到通知设置'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

package com.cq.jiguang_push_plugin;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 方法调用分发器
 * 负责处理所有来自 Dart 端的方法调用
 */
public class MethodCallDispatcher implements MethodCallHandler {
    private static final String TAG = "MethodCallDispatcher";
    private final Context context;
    private final JiguangPushPlugin plugin;

    private final PushManager pushManager;

    public MethodCallDispatcher(Context context, JiguangPushPlugin plugin) {
        this.context = context;
        this.plugin = plugin;
        this.pushManager = PushManager.getInstance();
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        try {
            switch (call.method) {
                case "getPlatformVersion":
                    result.success("Android " + android.os.Build.VERSION.RELEASE);
                    break;

                case "initPush":
                    handleInitPush(result);
                    break;

                case "getRegistrationId":
                    handleGetRegistrationId(result);
                    break;

                case "setAlias":
                    handleSetAlias(call, result);
                    break;

                case "deleteAlias":
                    handleDeleteAlias(call, result);
                    break;

                case "setTags":
                    handleSetTags(call, result);
                    break;

                case "addTags":
                    handleAddTags(call, result);
                    break;

                case "deleteTags":
                    handleDeleteTags(call, result);
                    break;

                case "cleanTags":
                    handleCleanTags(call, result);
                    break;

                case "getAllTags":
                    handleGetAllTags(call, result);
                    break;

                case "stopPush":
                    handleStopPush(result);
                    break;

                case "resumePush":
                    handleResumePush(result);
                    break;

                case "isPushStopped":
                    handleIsPushStopped(result);
                    break;
                case "isNotificationEnabled":
                    handleIsNotificationEnabled(result);
                    break;
                case "goToAppNotificationSettings":
                    handleGoToAppNotificationSettings(result);
                    break;
                case "checkPendingMessage":
                    handleCheckPendingMessage(result);
                    break;
                case "testPushMessage":
                    handleTestPushMessage(call, result);
                    break;
                default:
                    result.notImplemented();
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling method call: " + call.method, e);
            result.error("ERROR", "Failed to handle method call: " + e.getMessage(), null);
        }
    }


    /**
     * 处理跳转到应用通知设置
     *
     * @param result 结果
     */
    private void handleGoToAppNotificationSettings(Result result) {
        if (pushManager != null) {
            pushManager.goToAppNotificationSettings(context);
            result.success(1);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理检查通知是否开启
     *
     * @param result 结果
     */
    private void handleIsNotificationEnabled(Result result) {
        if (pushManager != null) {
            boolean isEnabled = pushManager.isNotificationEnabled();
            Log.e(TAG, "handleIsNotificationEnabled: " + isEnabled);
            result.success(isEnabled);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理初始化推送
     */
    private void handleInitPush(Result result) {
        if (pushManager != null) {
            pushManager.init(context);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Push initialized successfully");
            result.success(response);
        } else {
            result.error("INIT_ERROR", "PushManager is null", null);
        }
    }

    /**
     * 处理获取注册ID
     */
    private void handleGetRegistrationId(Result result) {
        if (pushManager != null) {
            String registrationId = pushManager.getRegistrationId();
            result.success(registrationId);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理设置别名
     */
    private void handleSetAlias(MethodCall call, Result result) {
        String alias = call.argument("alias");
        Integer sequence = call.argument("sequence");

        if (alias == null) {
            result.error("INVALID_ARGUMENT", "Alias cannot be null", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.setAlias(alias, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理删除别名
     */
    private void handleDeleteAlias(MethodCall call, Result result) {
        Integer sequence = call.argument("sequence");

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.deleteAlias(sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理设置标签
     */
    private void handleSetTags(MethodCall call, Result result) {
        List<String> tagList = call.argument("tags");
        Integer sequence = call.argument("sequence");

        if (tagList == null || tagList.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Tags cannot be null or empty", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        Set<String> tags = new HashSet<>(tagList);

        if (pushManager != null) {
            pushManager.setTags(tags, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理添加标签
     */
    private void handleAddTags(MethodCall call, Result result) {
        List<String> tagList = call.argument("tags");
        Integer sequence = call.argument("sequence");

        if (tagList == null || tagList.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Tags cannot be null or empty", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        Set<String> tags = new HashSet<>(tagList);

        if (pushManager != null) {
            pushManager.addTags(tags, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理删除标签
     */
    private void handleDeleteTags(MethodCall call, Result result) {
        List<String> tagList = call.argument("tags");
        Integer sequence = call.argument("sequence");

        if (tagList == null || tagList.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Tags cannot be null or empty", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        Set<String> tags = new HashSet<>(tagList);

        if (pushManager != null) {
            pushManager.deleteTags(tags, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理清除所有标签
     */
    private void handleCleanTags(MethodCall call, Result result) {
        Integer sequence = call.argument("sequence");

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.cleanTags(sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理获取所有标签
     */
    private void handleGetAllTags(MethodCall call, Result result) {
        Integer sequence = call.argument("sequence");

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.getAllTags(sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理停止推送
     */
    private void handleStopPush(Result result) {
        if (pushManager != null) {
            pushManager.stopPush();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Push service stopped");
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理恢复推送
     */
    private void handleResumePush(Result result) {
        if (pushManager != null) {
            pushManager.resumePush();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Push service resumed");
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理检查推送状态
     */
    private void handleIsPushStopped(Result result) {
        if (pushManager != null) {
            boolean isStopped = pushManager.isPushStopped();
            result.success(isStopped);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理检查暂存的推送消息
     */
    private void handleCheckPendingMessage(Result result) {
        try {
            if (plugin != null) {
                plugin.checkAndSendPendingMessage();
                result.success(null);
            } else {
                result.error("PLUGIN_NULL", "Plugin instance is null", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking pending message", e);
            result.error("ERROR", "Failed to check pending message: " + e.getMessage(), null);
        }
    }

    /**
     * 处理测试推送消息（用于调试）
     */
    private void handleTestPushMessage(MethodCall call, Result result) {
        try {
            String path = call.argument("path");
            if (path == null || path.isEmpty()) {
                path = "/test?id=123";
            }

            Map<String, Object> testMessage = new HashMap<>();
            testMessage.put("path", path);

            Map<String, Object> extras = new HashMap<>();
            extras.put("test", "true");
            extras.put("timestamp", String.valueOf(System.currentTimeMillis()));
            testMessage.put("extras", extras);

            Log.d(TAG, "Sending test push message: " + testMessage);

            if (plugin != null) {
                plugin.sendTestPushMessage(testMessage);
                result.success("Test message sent");
            } else {
                result.error("PLUGIN_NULL", "Plugin instance is null", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error sending test message", e);
            result.error("ERROR", "Failed to send test message: " + e.getMessage(), null);
        }
    }
}

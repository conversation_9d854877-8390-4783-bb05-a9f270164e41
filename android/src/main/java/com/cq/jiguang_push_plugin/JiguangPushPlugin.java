package com.cq.jiguang_push_plugin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;


import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JiguangPushPlugin
 */
public class JiguangPushPlugin implements FlutterPlugin, ActivityAware, PluginRegistry.NewIntentListener {
    private static final String TAG = "JiguangPushPlugin";

    private static final String PUSH_ACTION = "com.changqing.health";
    private static final String JPUSH_ACTION = "cn.jpush.android.intent.JNotifyActivity";

    private static final List<String> pushActions = List.of(PUSH_ACTION, JPUSH_ACTION);

    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private MethodChannel channel;
    private EventChannel eventChannel;
    private EventChannel.EventSink eventSink;
    private PushManager pushManager;

    private ActivityPluginBinding activityBinding;

    // 暂存冷启动推送消息
    private static Map<String, Object> pendingPushMessage = null;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        Context context = flutterPluginBinding.getApplicationContext();
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "jiguang_push_plugin");

        // 创建 EventChannel 用于推送消息
        eventChannel = new EventChannel(flutterPluginBinding.getBinaryMessenger(), "jiguang_push_plugin/push_message");
        eventChannel.setStreamHandler(new EventChannel.StreamHandler() {
            @Override
            public void onListen(Object arguments, EventChannel.EventSink events) {
                eventSink = events;
                Log.d(TAG, "EventChannel listener attached");
            }

            @Override
            public void onCancel(Object arguments) {
                eventSink = null;
                Log.d(TAG, "EventChannel listener cancelled");
            }
        });

        // 创建消息处理器，传入当前插件实例以便访问 EventChannel
        MethodCallDispatcher dispatcher = new MethodCallDispatcher(context, this);
        channel.setMethodCallHandler(dispatcher);

        // 初始化推送管理器
        pushManager = PushManager.getInstance();
//        pushManager.init(context);

        Log.d(TAG, "JiguangPushPlugin attached to engine");
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
        eventChannel.setStreamHandler(null);
        eventSink = null;
        if (pushManager != null) {
            pushManager.destroy();
        }
        Log.d(TAG, "JiguangPushPlugin detached from engine");
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activityBinding = binding;
        Activity activity = binding.getActivity();
        Intent intent = activity.getIntent();
        Log.e(TAG, "onAttachedToActivity: " + intent.getAction());
        if (pushActions.contains(intent.getAction())) {
            // 冷启动：暂存推送消息
            handlePushIntent(intent, false);
        }
        binding.addOnNewIntentListener(this);
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        if (activityBinding != null) {
            activityBinding.removeOnNewIntentListener(this);
        }
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activityBinding = binding;
        binding.addOnNewIntentListener(this);
    }

    @Override
    public void onDetachedFromActivity() {
        if (activityBinding != null) {
            activityBinding.removeOnNewIntentListener(this);
            activityBinding = null;
        }
    }

    /**
     * 处理新的意图
     *
     * @param intent The new intent that was started for the activity.
     * @return
     */
    @Override
    public boolean onNewIntent(@NonNull Intent intent) {
        Log.e(TAG, "onNewIntent: " + intent.getAction());
        if (pushActions.contains(intent.getAction())) {
            // 热启动：直接发送给 Flutter
            handlePushIntent(intent, true);
        }
        return true;
    }

    /**
     * 处理推送意图
     *
     * @param intent     推送意图
     * @param isHotStart 是否为热启动
     */
    private void handlePushIntent(Intent intent, boolean isHotStart) {
        // 解析推送数据
        String path = intent.getStringExtra("path");
        if (path == null || path.isEmpty()) {
            Log.w(TAG, "Push message path is null or empty");
            return;
        }

        // 创建推送消息数据
        Map<String, Object> pushMessage = new HashMap<>();
        pushMessage.put("path", path);

        // 解析 extras（可选）
        Map<String, Object> extras = new HashMap<>();
        // 这里可以根据实际需要解析更多的 extras 数据
        // 例如：extras.put("key", intent.getStringExtra("key"));
        if (!extras.isEmpty()) {
            pushMessage.put("extras", extras);
        }

        Log.d(TAG, "handlePushIntent: path=" + path + ", isHotStart=" + isHotStart);

        if (isHotStart) {
            // 热启动：直接通过 EventChannel 发送给 Flutter
            sendPushMessageToFlutter(pushMessage);
        } else {
            // 冷启动：暂存消息
            storePendingPushMessage(pushMessage);
        }
    }

    /**
     * 通过 EventChannel 发送推送消息给 Flutter
     *
     * @param pushMessage 推送消息数据
     */
    private void sendPushMessageToFlutter(Map<String, Object> pushMessage) {
        Log.e(TAG, "sendPushMessageToFlutter: Thread.currentThread: " + Thread.currentThread().getName());
        if (eventSink != null) {
            eventSink.success(pushMessage);
            Log.d(TAG, "Push message sent to Flutter via EventChannel");
        } else {
            Log.w(TAG, "EventSink is null, cannot send push message to Flutter");
        }
    }

    /**
     * 暂存冷启动推送消息
     *
     * @param pushMessage 推送消息数据
     */
    private void storePendingPushMessage(Map<String, Object> pushMessage) {
        pendingPushMessage = pushMessage;
        Log.d(TAG, "Push message stored for cold start");
    }

    /**
     * 获取暂存的推送消息
     *
     * @return 暂存的推送消息，如果没有则返回 null
     */
    public static Map<String, Object> getPendingPushMessage() {
        return pendingPushMessage;
    }

    /**
     * 清除暂存的推送消息
     */
    public static void clearPendingPushMessage() {
        pendingPushMessage = null;
        Log.d(TAG, "Pending push message cleared");
    }

    /**
     * 检查并发送暂存的推送消息
     */
    public void checkAndSendPendingMessage() {
        if (pendingPushMessage != null) {
            sendPushMessageToFlutter(pendingPushMessage);
            clearPendingPushMessage();
            Log.d(TAG, "Pending push message sent and cleared");
        } else {
            Log.d(TAG, "No pending push message to send");
        }
    }
}

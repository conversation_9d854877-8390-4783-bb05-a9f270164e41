# 推送消息调试指南

## 问题分析

你遇到的问题是 Android 端通过 `eventSink.success()` 发送消息，但 Flutter 端没有接收到。

## 已修复的问题

### 1. 线程问题
**问题**：EventSink 可能在非主线程中调用
**解决方案**：使用 `Handler(Looper.getMainLooper())` 确保在主线程中发送消息

### 2. 监听时机问题
**问题**：Flutter 端可能还没开始监听，原生端就发送了消息
**解决方案**：
- 在 EventChannel 监听器附加时，自动检查并发送暂存消息
- 如果 EventSink 为空，自动暂存消息

### 3. 数据格式问题
**问题**：推送数据格式可能不正确
**解决方案**：
- 改进数据解析逻辑
- 添加详细的调试日志
- 支持解析所有 Intent extras

## 调试步骤

### 1. 使用测试功能
在示例应用中点击"发送测试推送消息"按钮，这会：
- 创建一个测试推送消息
- 直接通过 EventChannel 发送给 Flutter
- 验证整个通信链路是否正常

### 2. 检查日志输出

#### Android 端日志（使用 `adb logcat`）
```bash
adb logcat | grep JiguangPushPlugin
```

关键日志：
- `EventChannel listener attached, eventSink: not null` - EventChannel 监听器已附加
- `Parsed push message: {path=..., extras=...}` - 推送消息解析成功
- `Sending push message to Flutter: {...}` - 正在发送消息到 Flutter
- `Push message sent to Flutter via EventChannel` - 消息已发送

#### Flutter 端日志
在 Debug 模式下，Flutter 控制台会显示：
- `Starting to listen to push message EventChannel` - 开始监听
- `Received push message data: {...}` - 接收到原始数据
- `Parsed push message: PushMessage{...}` - 解析后的消息

### 3. 检查 EventChannel 状态

确保以下步骤按顺序执行：
1. Flutter 端调用 `setPushMessageCallback()` 
2. Android 端日志显示 "EventChannel listener attached"
3. 发送推送消息或调用测试功能

### 4. 验证推送数据格式

确保 Intent 中包含正确的数据：
```java
Intent intent = new Intent();
intent.setAction("com.changqing.health"); // 或 "cn.jpush.android.intent.JNotifyActivity"
intent.putExtra("path", "/goodsDetail?id=123");
// 其他额外数据...
```

## 常见问题排查

### 问题1：EventSink 为 null
**现象**：日志显示 "EventSink is null, cannot send push message to Flutter"
**原因**：Flutter 端还没有开始监听 EventChannel
**解决方案**：
1. 确保在应用完全启动后再设置回调
2. 消息会自动暂存，调用 `checkPendingMessage()` 获取

### 问题2：Flutter 端没有接收到数据
**现象**：Android 端发送成功，但 Flutter 端没有日志
**排查步骤**：
1. 检查 EventChannel 名称是否一致：`jiguang_push_plugin/push_message`
2. 确认 Flutter 端已调用 `setPushMessageCallback()`
3. 检查是否有异常被捕获但没有显示

### 问题3：数据格式错误
**现象**：Flutter 端接收到数据但解析失败
**排查步骤**：
1. 检查 Android 端日志中的 "Parsed push message"
2. 确认数据包含 `path` 字段
3. 检查 `extras` 是否为有效的 Map 格式

## 测试建议

### 1. 分步测试
1. 先测试 EventChannel 通信（使用测试按钮）
2. 再测试真实推送消息
3. 分别测试热启动和冷启动场景

### 2. 使用模拟推送
创建一个测试 Activity 来模拟推送：
```java
Intent intent = new Intent();
intent.setAction("com.changqing.health");
intent.putExtra("path", "/test?id=123");
startActivity(intent);
```

### 3. 检查权限和配置
确保：
- 应用有通知权限
- Intent Filter 配置正确
- 推送服务正常运行

## 联系支持

如果问题仍然存在，请提供：
1. 完整的 Android 端日志
2. Flutter 端控制台输出
3. 具体的推送数据格式
4. 复现步骤
